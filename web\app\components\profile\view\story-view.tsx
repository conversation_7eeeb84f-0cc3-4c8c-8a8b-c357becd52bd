import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from '#app/components/ui/avatar.js';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from '#app/components/ui/dialog.js';
import {
  PhotoWithSignedUrl,
  SerializedVideo,
} from '#app/types/serialized-types.js';
import { Button } from '@headlessui/react';
import {
  ChevronLeft,
  ChevronRight,
  EyeIcon,
  Heart,
  Send,
  Volume2,
  VolumeX,
  X,
} from 'lucide-react';
import { Input } from '../../forms/inputs';
import { useCallback, useRef, useState } from 'react';
import { cn } from '#app/utils/misc.js';

interface StoryViewProps {
  firstName: string;
  profile?: PhotoWithSignedUrl | null;
  stories: SerializedVideo[];
}

export function StoryView({ stories, profile, firstName }: StoryViewProps) {
  const [currentStory, setCurrentStory] = useState(0);
  const [progress, setProgress] = useState(0);
  const [isMuted, setIsMuted] = useState(true);

  const totalStories = stories.length || 0;
  const isFirstStory = currentStory === 0;
  const isLastStory = currentStory === totalStories - 1 || totalStories === 0;

  const handleNextStory = () => {
    setCurrentStory((prev) => (prev + 1) % totalStories);
  };

  const handlePrevStory = () => {
    setCurrentStory((prev) => (prev - 1 + totalStories) % totalStories);
  };

  const handleToggleMute = () => {
    setIsMuted(!isMuted);
  };

  const currentStoryData = stories[currentStory];

  const handleProgress = (progress: number) => {
    setProgress(progress);
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button className="absolute top-4 left-4 bg-white text-brand-accent-tertiary rounded-sm p-2 flex justify-around gap-2 w-[111px] max-h-7 items-center text-sm">
          <EyeIcon className="w-4 h-4" />
          <span>View story</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="w-full h-full bg-transparent border-none max-w-5xl">
        <div className="relative w-full h-full max-w-5xl grid place-self-center max-h-[calc(100%-8rem)]">
          <div className="absolute top-0 left-0 right-0 bottom-0 z-0 w-full h-fit max-w-2xl place-self-center place-content-center">
            <div className="grid grid-cols-4 p-4">
              <Button
                onClick={handlePrevStory}
                className={cn('col-start-1', isFirstStory ? 'hidden' : '')}
              >
                <ChevronLeft className="w-16 h-16 text-zinc-400" />
              </Button>
              <Button
                onClick={handleNextStory}
                className={cn(
                  'col-start-5 col-end-5',
                  isLastStory ? 'hidden' : '',
                )}
              >
                <ChevronRight className="w-16 h-16 text-zinc-400" />
              </Button>
            </div>
          </div>
          <div className="max-w-[391px] bg-zinc-500 rounded-[20px] grid h-full w-full place-self-center overflow-hidden">
            <div className="relative p-5">
              <div className="relative flex flex-col gap-4 z-50">
                <div className="grid grid-flow-col gap-2">
                  {Array(totalStories)
                    .fill(0)
                    .map((_, index) => (
                      <StoryIndicator
                        key={index}
                        isViewing={index <= currentStory}
                        isCompleted={index < currentStory}
                        progress={progress}
                      />
                    ))}
                </div>
                <div className="w-full h-fit flex justify-between items-center">
                  <DialogTitle asChild>
                    <div className="flex items-center gap-2">
                      <Avatar>
                        <AvatarImage src={profile?.smallUrl ?? ''} />
                        <AvatarFallback>{firstName}</AvatarFallback>
                      </Avatar>
                      <div className="text-white font-light">{firstName}</div>
                      <div className="text-gray-300">3h</div>
                    </div>
                  </DialogTitle>
                  <div className="pr-3">
                    <Button onClick={handleToggleMute}>
                      {isMuted ? (
                        <VolumeX className="w-6 h-6 font-light text-muted" />
                      ) : (
                        <Volume2 className="w-6 h-6 font-light text-muted" />
                      )}
                    </Button>
                  </div>
                </div>
              </div>
              <StoryItem
                story={currentStoryData}
                isMuted={isMuted}
                onProgress={handleProgress}
                onFinish={handleNextStory}
              />
              {/* <div className="absolute w-full h-full inset-0 z-1">
                <video
                  autoPlay
                  muted={isMuted}
                  loop
                  className="w-full h-full object-cover"
                >
                  <source src={currentStoryData?.url} type="video/mp4" />
                </video>
              </div> */}
              <div className="w-full h-full">
                <div className="absolute bottom-0 left-0 right-0 p-4 w-full">
                  <div className="flex items-center justify-between w-full gap-4">
                    <div className="relative rounded-full border-border border-px flex-1 group">
                      <Input
                        type="text"
                        className="rounded-full placeholder:text-border focus:bg-transparent hover:bg-transparent"
                        placeholder={`Reply to ${firstName}...`}
                      />
                      <div className="absolute right-0 top-0 bottom-0 flex items-center pr-4">
                        <Button>
                          <Send className="w-6 h-6 font-light text-border" />
                        </Button>
                      </div>
                    </div>
                    <div className="flex">
                      <Button>
                        <Heart className="w-8 h-8 font-light text-border" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <DialogClose className="absolute top-0 right-0 p-4" asChild>
            <Button>
              <X className="w-8 h-8 text-white" />
            </Button>
          </DialogClose>
        </div>
      </DialogContent>
    </Dialog>
  );
}

interface StoryIndicatorProps {
  isViewing: boolean;
  isCompleted: boolean;
  progress: number;
}

function StoryIndicator({
  isViewing,
  isCompleted,
  progress,
}: StoryIndicatorProps) {
  const width = isCompleted ? 100 : isViewing ? progress : 0;

  return (
    <div className="relative w-full h-0.5 bg-gray-300 rounded-full overflow-hidden">
      <div
        className="absolute inset-0 w-full h-full bg-white"
        style={{ width }}
      ></div>
    </div>
  );
}

interface StoryItemProps {
  isMuted: boolean;
  story?: SerializedVideo;
  onFinish: () => void;
  onProgress: (progress: number) => void;
}

function StoryItem({ story, onFinish, isMuted, onProgress }: StoryItemProps) {
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const videoIntervalRef = useRef<NodeJS.Timeout>();

  const currentStoryRef = useCallback(
    (element: HTMLVideoElement | null) => {
      videoRef.current = element;
      if (!element) {
        if (!videoIntervalRef.current) return;
        clearInterval(videoIntervalRef.current);
        return;
      }

      const interval = 50;
      videoIntervalRef.current = setInterval(() => {
        onProgress((element.currentTime / element.duration) * 100);
      }, interval);
      element.onended = () => {
        onFinish();
      };
    },
    [onFinish],
  );

  return (
    <div className="absolute w-full h-full inset-0 z-1">
      <video
        autoPlay
        muted={isMuted}
        loop
        className="w-full h-full object-cover"
        ref={currentStoryRef}
      >
        <source src={story?.url} type="video/mp4" />
      </video>
    </div>
  );
}
