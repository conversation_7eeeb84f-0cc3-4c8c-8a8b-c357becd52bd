import { type SerializedFullProfile } from '#app/types/serialized-types.js';
import { VideoType } from '@prisma/client';
import { StoryView } from './story-view';

type Props = {
  profile: SerializedFullProfile;
};

function ProfilePhotoWidget({ profile }: Props) {
  const hasStory = profile?.galleryVideos.some(
    (video) => video.videoType === VideoType.STORY,
  );
  const stories = hasStory
    ? profile.galleryVideos.filter(
        (video) => video.videoType === VideoType.STORY,
      )
    : [];

  return (
    <>
      <div className="relative">
        <div className="relative w-[320px] h-[350px] rounded-[20px] overflow-hidden">
          <img
            src={
              profile?.mainPhoto?.smallUrl ||
              'https://www.gravatar.com/avatar/94d093eda664addd6e450d7e9881bcad?s=320&d=identicon&r=PG'
            }
            alt="Profile picture"
            className="w-full h-full object-cover object-center absolute inset-0"
          />

          {hasStory ? (
            <StoryView
              firstName={profile.firstName}
              profile={profile.mainPhoto}
              stories={stories}
            />
          ) : null}
        </div>
      </div>
    </>
  );
}

export { ProfilePhotoWidget };
