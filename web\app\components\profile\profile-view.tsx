import { useMutation } from '@tanstack/react-query';
import { useEffect } from 'react';
import { useProfile } from '#app/contexts/profile-context.js';
import { type SerializedFullProfile } from '#app/types/serialized-types';
import { apiPost } from '#app/utils/fetch.client.js';
import { type InterestType } from '#shared/types/interests.js';

import {
  AppearanceWidget,
  DatingPreferenceWidget,
  LifestyleWidget,
  ProfileOverviewWidget,
} from './view';
import { InterestsWidget } from './view/interests-widget';
import { ProfilePhotoWidget } from './view/profile-photo-widget';
import ReadonlyGalleryPhotos from './view/readonly-gallery-widget';
import { RecommendedProfilesWidget } from './view/recommended-profiles-widget';

interface ProfileViewProps {
  profile: SerializedFullProfile;
  recommendedProfiles: SerializedFullProfile[];
  isPopup?: boolean;
}

const ProfileView: React.FC<ProfileViewProps> = ({
  profile,
  recommendedProfiles,
}) => {
  const { profile: userProfile } = useProfile();
  const isMyProfile = profile.id === userProfile?.id;
  const viewedProfileId = profile.id;
  const viewedUserId = profile.userId;
  const viewerProfileId = userProfile?.id;

  const {
    bio,
    location,
    datingPreference,
    lifestyle,
    appearance,
    firstName,
    emailIsVerified,
    age,
  } = profile || {};

  const viewProfileMutation = useMutation({
    mutationFn: () =>
      apiPost(`/api/profile/${viewedProfileId}/view`, {
        viewerProfileId,
        viewedUserId,
      }),
    onError: (error) => {
      console.error('Error viewing profile:', error);
    },
    onSuccess: () => {
      console.log('Profile view logged successfully');
    },
  });

  // Trigger the profile view mutation only on the client side
  useEffect(() => {
    // Trigger the mutation only if viewerProfileId is defined and it's not the user's own profile
    if (
      viewerProfileId &&
      !isMyProfile &&
      !viewProfileMutation.isSuccess &&
      !viewProfileMutation.isPending
    ) {
      viewProfileMutation.mutate();
    }
  }, [viewerProfileId, isMyProfile, viewProfileMutation]);

  return (
    <div className="flex gap-4 mx-auto w-[90%] md:w-[80%] lg:w-[70%]  lg:flex-row flex-col my-8">
      <div className="flex flex-col gap-12 bg-white mt-8 rounded-xl p-10">
        <div className="flex gap-4 md:flex-row flex-col lg:flex-row ">
          <ProfilePhotoWidget profile={profile} />
          <ProfileOverviewWidget
            firstName={firstName}
            age={age}
            tagLine={bio?.tagline}
            occupation={bio?.occupation}
            location={location}
            isVerified={emailIsVerified}
            bio={bio?.aboutMe}
            isOnline={true}
          />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <AppearanceWidget appearance={appearance} />
          <LifestyleWidget lifestyle={lifestyle} />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <DatingPreferenceWidget datingPreference={datingPreference} />
          <InterestsWidget interests={lifestyle?.interests as InterestType[]} />
        </div>

        <div className="">
          <ReadonlyGalleryPhotos galleryPhotos={profile.galleryPhotos} />
        </div>
      </div>
      {/* grid size small */}
      <div className="mt-8 bg-white rounded-xl p-6 h-fit">
        <RecommendedProfilesWidget recommendedProfiles={recommendedProfiles} />
      </div>
    </div>
  );
};

export default ProfileView;
